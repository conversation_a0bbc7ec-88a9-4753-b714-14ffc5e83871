/* Database Store Components */

/* Category Cards */
.category-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.category-description {
    color: var(--gray-600);
    font-size: 0.95rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-count {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
}

/* Product Cards */
.product-card {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.product-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem;
    position: relative;
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.product-category {
    opacity: 0.9;
    font-size: 0.9rem;
}

.product-body {
    padding: 1.5rem;
}

.product-description {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.product-features {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.product-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.product-features i {
    color: var(--success-color);
    font-size: 0.8rem;
}

.product-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-box {
    text-align: center;
    padding: 1rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--gray-600);
}

.product-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.product-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.price-current {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-original {
    font-size: 1rem;
    color: var(--gray-500);
    text-decoration: line-through;
}

.price-discount {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.product-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-add-cart {
    flex: 1;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-add-cart:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-view-details {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
}

.btn-view-details:hover {
    background: var(--primary-color);
    color: white;
}

/* Progress Bar Component */
.progress-container {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    margin: 2rem 0;
}

.progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.progress-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-percentage {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.progress-bar-container {
    position: relative;
    background: var(--gray-200);
    border-radius: var(--border-radius);
    height: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-status {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.progress-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.progress-controls .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Cart Component */
.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    background: white;
}

.cart-item-info {
    flex: 1;
}

.cart-item-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.cart-item-category {
    font-size: 0.9rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.cart-item-price {
    font-weight: 700;
    color: var(--primary-color);
}

.cart-item-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

.quantity-btn {
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: var(--gray-200);
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: none;
    background: transparent;
    font-weight: 600;
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.cart-summary {
    background: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.summary-row.total {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
    border-top: 1px solid var(--gray-300);
    padding-top: 0.75rem;
    margin-top: 1rem;
}

/* Filter Component */
.filter-sidebar {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.filter-section {
    margin-bottom: 2rem;
}

.filter-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray-200);
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.filter-option:hover {
    background: var(--gray-100);
}

.filter-option input[type="checkbox"],
.filter-option input[type="radio"] {
    margin: 0;
}

.filter-option label {
    cursor: pointer;
    flex: 1;
    margin: 0;
}

.filter-count {
    background: var(--gray-200);
    color: var(--gray-700);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.price-range-slider {
    margin: 1rem 0;
}

.price-range-slider input[type="range"] {
    width: 100%;
    margin: 0.5rem 0;
}

.price-range-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--gray-600);
    margin-top: 0.5rem;
}

/* Notification Component */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-message {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin: 0;
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-300) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.skeleton-header {
    height: 60px;
    margin-bottom: 1rem;
}

.skeleton-title {
    height: 20px;
    width: 70%;
    margin-bottom: 0.5rem;
}

.skeleton-text {
    height: 16px;
    width: 100%;
    margin-bottom: 0.5rem;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-button {
    height: 40px;
    width: 120px;
    margin-top: 1rem;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
    background: white;
}

.feature-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-card .feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-card h4 {
    color: var(--gray-800);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
}

/* Testimonials Section */
.testimonials-section {
    padding: 5rem 0;
    background: var(--gray-100);
}

.testimonial-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    height: 100%;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
    justify-content: center;
}

.stars i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

.testimonial-content p {
    color: var(--gray-700);
    font-style: italic;
    line-height: 1.6;
    margin: 0;
    text-align: center;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author-info {
    text-align: center;
}

.author-info h5 {
    color: var(--gray-800);
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.author-info span {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* FAQ Section */
.faq-section {
    padding: 5rem 0;
    background: white;
}

.accordion {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.accordion-item {
    border: none;
    border-bottom: 1px solid var(--gray-200);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-button {
    background: white;
    color: var(--gray-800);
    font-weight: 600;
    padding: 1.5rem;
    border: none;
    font-family: var(--font-secondary);
    text-align: right;
    direction: rtl;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-button::after {
    margin-left: auto;
    margin-right: 0;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
    padding: 1.5rem;
    background: var(--gray-50);
    color: var(--gray-700);
    line-height: 1.6;
}

/* Product Modal */
.modal-xl .modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-title {
    font-family: var(--font-secondary);
    font-weight: 700;
}

.product-details {
    padding: 2rem;
}

.product-gallery {
    margin-bottom: 2rem;
}

.product-info {
    margin-bottom: 2rem;
}

.product-specs {
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-label {
    font-weight: 600;
    color: var(--gray-700);
}

.spec-value {
    color: var(--gray-600);
}

/* View Toggle */
.view-toggle {
    display: flex;
    justify-content: center;
}

.view-toggle .btn-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.view-toggle .btn {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: white;
    padding: 0.5rem 1rem;
}

.view-toggle .btn.active {
    background: var(--primary-color);
    color: white;
}

/* Products List View */
.products-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.products-list .product-card {
    display: flex;
    flex-direction: row;
    align-items: stretch;
}

.products-list .product-header {
    flex: 0 0 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.products-list .product-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.products-list .product-footer {
    flex: 0 0 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .product-stats {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .cart-item-actions {
        width: 100%;
        justify-content: space-between;
    }

    .filter-sidebar {
        position: static;
        margin-bottom: 2rem;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }

    .feature-card {
        margin-bottom: 2rem;
    }

    .testimonial-card {
        margin-bottom: 2rem;
    }

    .products-list .product-card {
        flex-direction: column;
    }

    .products-list .product-header,
    .products-list .product-footer {
        flex: none;
    }

    .view-toggle {
        margin-top: 1rem;
    }
}
