// Database Categories and Specializations

// Wait for data to be loaded from JSON files
document.addEventListener('databaseDataLoaded', function() {
    console.log('Database data loaded, initializing categories...');
    if (typeof loadCategories === 'function') {
        loadCategories();
    }
    if (typeof loadProducts === 'function') {
        loadProducts();
    }
});

// Legacy categories data structure (kept for fallback)
const legacyCategoriesData = {
    medical: {
        name: 'الطبية والصحية',
        icon: 'fas fa-user-md',
        description: 'قواعد بيانات للمهن الطبية والصحية',
        specializations: [
            { name: 'الأطباء والاستشاريين', count: 2500, price: 299, title: 'قاعدة بيانات شاملة للأطباء والاستشاريين في المملكة العربية السعودية', description: 'تضم أكثر من 2500 طبيب واستشاري في جميع التخصصات الطبية بالمملكة مع بيانات الاتصال المحدثة' },
            { name: 'المستشفيات والمراكز الطبية', count: 450, price: 199, title: 'قاعدة بيانات المستشفيات والمراكز الطبية الخاصة والحكومية', description: 'دليل شامل لأكثر من 450 مستشفى ومركز طبي في دول الخليج مع معلومات الإدارة والتواصل' },
            { name: 'العيادات الطبية المتخصصة', count: 1200, price: 149, title: 'قاعدة بيانات العيادات الطبية المتخصصة في المنطقة الخليجية', description: 'تشمل 1200 عيادة طبية متخصصة مع بيانات الأطباء المسؤولين وأرقام التواصل المباشر' },
            { name: 'الممرضين والممرضات', count: 3200, price: 179, title: 'قاعدة بيانات شاملة للممرضين والممرضات المرخصين', description: 'أكبر قاعدة بيانات للكوادر التمريضية تضم أكثر من 3200 ممرض وممرضة مرخصين في القطاع الصحي' },
            { name: 'أخصائيي العلاج الطبيعي', count: 800, price: 129, title: 'قاعدة بيانات أخصائيي العلاج الطبيعي والتأهيل الطبي', description: 'تضم 800 أخصائي علاج طبيعي وتأهيل طبي معتمدين في المراكز والعيادات المتخصصة' },
            { name: 'مراكز العلاج والتأهيل', count: 1500, price: 159, title: 'قاعدة بيانات مراكز العلاج والتأهيل الطبي المتخصصة', description: 'دليل شامل لـ 1500 مركز علاج وتأهيل طبي مع بيانات الإدارة والأطباء المتخصصين' }
        ]
    },
    engineering: {
        name: 'الهندسة والتقنية',
        icon: 'fas fa-cogs',
        description: 'قواعد بيانات للمهندسين والتقنيين',
        specializations: [
            { name: 'المهندسين في جميع التخصصات', count: 4200, price: 349, title: 'قاعدة بيانات شاملة للمهندسين في جميع التخصصات الهندسية', description: 'تضم أكثر من 4200 مهندس في تخصصات مختلفة: مدني، كهربائي، ميكانيكي، معماري، وغيرها في دول الخليج' },
            { name: 'المهندسين الاستشاريين', count: 3800, price: 329, title: 'قاعدة بيانات المهندسين الاستشاريين والخبراء التقنيين', description: 'دليل حصري لـ 3800 مهندس استشاري وخبير تقني في الشركات الكبرى ومكاتب الاستشارات الهندسية' },
            { name: 'قطاع الصناعات التحويلية', count: 1600, price: 249, title: 'قاعدة بيانات العاملين في قطاع الصناعات التحويلية والتصنيع', description: 'تشمل 1600 جهة اتصال في المصانع والشركات الصناعية ومراكز التصنيع والإنتاج' },
            { name: 'صناعة البتروكيماويات', count: 900, price: 399, title: 'قاعدة بيانات متخصصة في صناعة البتروكيماويات والمواد الكيميائية', description: 'دليل حصري لـ 900 متخصص في شركات البتروكيماويات والصناعات الكيميائية في المنطقة' },
            { name: 'قطاع الطاقة والمرافق', count: 1200, price: 279, title: 'قاعدة بيانات شاملة للعاملين في قطاع الطاقة والمرافق العامة', description: 'تضم 1200 جهة اتصال في شركات الكهرباء والمياه والطاقة المتجددة والمرافق العامة' },
            { name: 'صناعة النفط والغاز', count: 800, price: 459, title: 'قاعدة بيانات حصرية للعاملين في صناعة النفط والغاز الطبيعي', description: 'أهم قاعدة بيانات تضم 800 متخصص في شركات النفط والغاز والاستكشاف والإنتاج' },
            { name: 'شركات الغاز الطبيعي', count: 600, price: 389, title: 'قاعدة بيانات متخصصة في شركات الغاز الطبيعي والتوزيع', description: 'تشمل 600 جهة اتصال في شركات توزيع وتسويق الغاز الطبيعي والمحطات المتخصصة' },
            { name: 'قطاع التعدين والمناجم', count: 400, price: 299, title: 'قاعدة بيانات العاملين في قطاع التعدين واستخراج المعادن', description: 'دليل شامل لـ 400 متخصص في شركات التعدين واستخراج المعادن والمواد الخام' },
            { name: 'شركات التعدين المحلية', count: 350, price: 279, title: 'قاعدة بيانات شركات التعدين والمحاجر المحلية في المملكة', description: 'تضم 350 جهة اتصال في شركات التعدين المحلية ومحاجر الرمل والحصى والمواد الإنشائية' }
        ]
    },
    business: {
        name: 'الأعمال والإدارة',
        icon: 'fas fa-briefcase',
        description: 'قواعد بيانات لرجال الأعمال والإدارة',
        specializations: [
            { name: 'المدراء التنفيذيين والإداريين', count: 5200, price: 399, title: 'قاعدة بيانات شاملة للمدراء التنفيذيين والإداريين في القطاع الخاص', description: 'تضم أكثر من 5200 مدير تنفيذي وإداري في الشركات والمؤسسات الكبرى بدول الخليج' },
            { name: 'مدراء الفنادق والضيافة', count: 320, price: 199, title: 'قاعدة بيانات مدراء الفنادق ومنتجعات الضيافة والسياحة', description: 'دليل متخصص لـ 320 مدير فندق ومنتجع سياحي مع بيانات الاتصال المباشر' },
            { name: 'خبراء إدارة الأعمال', count: 2800, price: 349, title: 'قاعدة بيانات خبراء ومستشاري إدارة الأعمال والتطوير المؤسسي', description: 'تشمل 2800 خبير ومستشار في إدارة الأعمال والتطوير المؤسسي والاستراتيجي' },
            { name: 'الإدارة العليا والوسطى', count: 3200, price: 329, title: 'قاعدة بيانات شاملة للإدارة العليا والوسطى في الشركات الكبرى', description: 'دليل حصري لـ 3200 من الإدارة العليا والوسطى في أكبر الشركات والمؤسسات' },
            { name: 'مدراء الإدارات المتخصصة', count: 2900, price: 319, title: 'قاعدة بيانات مدراء الإدارات المتخصصة والأقسام الفنية', description: 'تضم 2900 مدير إدارة متخصصة: الموارد البشرية، المالية، التسويق، العمليات' },
            { name: 'الرؤساء التنفيذيين', count: 450, price: 599, title: 'قاعدة بيانات حصرية للرؤساء التنفيذيين ومؤسسي الشركات', description: 'أهم قاعدة بيانات تضم 450 رئيس تنفيذي ومؤسس شركة في المنطقة الخليجية' },
            { name: 'رواد الأعمال والمبتكرين', count: 800, price: 449, title: 'قاعدة بيانات رواد الأعمال والمبتكرين في الشركات الناشئة', description: 'دليل شامل لـ 800 رائد أعمال ومبتكر في الشركات الناشئة والتقنية' },
            { name: 'رواد الأعمال الشباب', count: 650, price: 429, title: 'قاعدة بيانات رواد الأعمال الشباب وأصحاب المشاريع الصغيرة', description: 'تشمل 650 رائد أعمال شاب وصاحب مشروع صغير ومتوسط في مختلف القطاعات' },
            { name: 'رواد الأعمال المتقدمين', count: 420, price: 399, title: 'قاعدة بيانات رواد الأعمال المتقدمين وأصحاب الشركات الناجحة', description: 'دليل حصري لـ 420 رائد أعمال متقدم وصاحب شركة ناجحة مع سنوات خبرة طويلة' },
            { name: 'الشركات والمؤسسات التجارية', count: 6500, price: 499, title: 'قاعدة بيانات شاملة للشركات والمؤسسات التجارية الكبرى', description: 'أكبر قاعدة بيانات تضم 6500 شركة ومؤسسة تجارية مع بيانات الإدارة والتواصل' },
            { name: 'المؤسسات الحكومية والخاصة', count: 4200, price: 379, title: 'قاعدة بيانات المؤسسات الحكومية والخاصة ومراكز الخدمات', description: 'تشمل 4200 مؤسسة حكومية وخاصة مع بيانات المسؤولين وأرقام التواصل' },
            { name: 'المؤسسات الدولية والإقليمية', count: 2800, price: 299, title: 'قاعدة بيانات المؤسسات الدولية والإقليمية العاملة في الخليج', description: 'دليل متخصص لـ 2800 مؤسسة دولية وإقليمية لها فروع أو مكاتب في المنطقة' }
        ]
    },
    education: {
        name: 'التعليم والتدريب',
        icon: 'fas fa-graduation-cap',
        description: 'قواعد بيانات للمؤسسات التعليمية',
        specializations: [
            { name: 'المعلمين والمعلمات', count: 8500, price: 249, title: 'قاعدة بيانات شاملة للمعلمين والمعلمات في جميع المراحل التعليمية', description: 'أكبر قاعدة بيانات تضم 8500 معلم ومعلمة في المدارس الحكومية والأهلية والدولية' },
            { name: 'المدارس والمؤسسات التعليمية', count: 1200, price: 199, title: 'قاعدة بيانات المدارس والمؤسسات التعليمية الحكومية والخاصة', description: 'دليل شامل لـ 1200 مدرسة ومؤسسة تعليمية مع بيانات الإدارة والتواصل' },
            { name: 'المدارس الأهلية والدولية', count: 2800, price: 179, title: 'قاعدة بيانات المدارس الأهلية والدولية ومدارس اللغات', description: 'تشمل 2800 مدرسة أهلية ودولية مع بيانات المدراء والمسؤولين' },
            { name: 'الجمعيات التعليمية والخيرية', count: 450, price: 149, title: 'قاعدة بيانات الجمعيات التعليمية والخيرية ومراكز التدريب', description: 'دليل متخصص لـ 450 جمعية تعليمية وخيرية ومركز تدريب مهني' },
            { name: 'شركات التوظيف والموارد البشرية', count: 1800, price: 229, title: 'قاعدة بيانات شركات التوظيف ومكاتب الموارد البشرية المتخصصة', description: 'تضم 1800 شركة توظيف ومكتب موارد بشرية مع بيانات المستشارين والمختصين' }
        ]
    },
    finance: {
        name: 'المالية والمصرفية',
        icon: 'fas fa-university',
        description: 'قواعد بيانات للقطاع المالي والمصرفي',
        specializations: [
            { name: 'البنوك والمصارف الرئيسية', count: 850, price: 399, title: 'قاعدة بيانات البنوك والمصارف الرئيسية في دول الخليج', description: 'دليل حصري لـ 850 بنك ومصرف مع بيانات الإدارة العليا والمدراء التنفيذيين' },
            { name: 'العاملين في القطاع المصرفي', count: 3200, price: 349, title: 'قاعدة بيانات شاملة للعاملين في القطاع المصرفي والمالي', description: 'تضم 3200 موظف وخبير مصرفي في مختلف الإدارات والأقسام المصرفية' },
            { name: 'البنوك التجارية والاستثمارية', count: 1200, price: 379, title: 'قاعدة بيانات البنوك التجارية والاستثمارية والإسلامية', description: 'تشمل 1200 جهة اتصال في البنوك التجارية والاستثمارية والمصرفية الإسلامية' },
            { name: 'المصارف المحلية والإقليمية', count: 650, price: 359, title: 'قاعدة بيانات المصارف المحلية والإقليمية ومؤسسات التمويل', description: 'دليل متخصص لـ 650 مصرف محلي وإقليمي ومؤسسة تمويل متخصصة' },
            { name: 'المحاسبين والمراجعين', count: 4500, price: 299, title: 'قاعدة بيانات شاملة للمحاسبين والمراجعين المعتمدين', description: 'أكبر قاعدة بيانات تضم 4500 محاسب ومراجع معتمد في الشركات والمكاتب المحاسبية' },
            { name: 'الشركات المالية والاستثمارية', count: 1800, price: 329, title: 'قاعدة بيانات الشركات المالية والاستثمارية وصناديق الاستثمار', description: 'تشمل 1800 شركة مالية واستثمارية مع بيانات المدراء والمستشارين الماليين' },
            { name: 'شركات التأمين والحماية', count: 1200, price: 249, title: 'قاعدة بيانات شركات التأمين والحماية والخدمات المالية', description: 'دليل شامل لـ 1200 شركة تأمين وحماية مع بيانات الوكلاء والمسوقين' },
            { name: 'الصرافات ومحلات الذهب', count: 800, price: 179, title: 'قاعدة بيانات الصرافات ومحلات الذهب والمجوهرات', description: 'تضم 800 صرافة ومحل ذهب ومجوهرات مع بيانات الملاك والمدراء' },
            { name: 'محلات الصرافة المحلية', count: 450, price: 199, title: 'قاعدة بيانات محلات الصرافة المحلية ومكاتب التحويل', description: 'دليل متخصص لـ 450 محل صرافة محلي ومكتب تحويل أموال معتمد' }
        ]
    },
    tourism: {
        name: 'السياحة والسفر',
        icon: 'fas fa-plane',
        description: 'قواعد بيانات لقطاع السياحة والسفر',
        specializations: [
            { name: 'العاملين في قطاع السياحة والسفر', count: 2200, price: 299, title: 'قاعدة بيانات شاملة للعاملين في قطاع السياحة والسفر', description: 'تضم 2200 موظف ومختص في شركات السياحة ووكالات السفر والمرشدين السياحيين' },
            { name: 'شركات السياحة الدولية', count: 1800, price: 279, title: 'قاعدة بيانات شركات السياحة الدولية ووكالات السفر الكبرى', description: 'دليل حصري لـ 1800 شركة سياحة دولية ووكالة سفر معتمدة في المنطقة' },
            { name: 'مكاتب السياحة المحلية', count: 1500, price: 259, title: 'قاعدة بيانات مكاتب السياحة المحلية والمرشدين السياحيين', description: 'تشمل 1500 مكتب سياحة محلي ومرشد سياحي معتمد في المملكة ودول الخليج' },
            { name: 'وكالات السفر والطيران', count: 1200, price: 239, title: 'قاعدة بيانات وكالات السفر وشركات الطيران والنقل السياحي', description: 'دليل شامل لـ 1200 وكالة سفر وشركة طيران ونقل سياحي مع بيانات المدراء' },
            { name: 'شركات السفر والرحلات', count: 1600, price: 269, title: 'قاعدة بيانات شركات السفر والرحلات السياحية المنظمة', description: 'تضم 1600 شركة سفر ورحلات سياحية منظمة مع بيانات منظمي الرحلات' },
            { name: 'الفنادق والمنتجعات السياحية', count: 950, price: 219, title: 'قاعدة بيانات الفنادق والمنتجعات السياحية والشاليهات', description: 'دليل متخصص لـ 950 فندق ومنتجع سياحي وشاليه مع بيانات الإدارة والحجوزات' },
            { name: 'قطاع الفندقة والضيافة', count: 1100, price: 229, title: 'قاعدة بيانات شاملة لقطاع الفندقة والضيافة والخدمات السياحية', description: 'تشمل 1100 جهة في قطاع الفندقة والضيافة مع بيانات المدراء والمسؤولين' },
            { name: 'شركات الحج والعمرة', count: 800, price: 199, title: 'قاعدة بيانات شركات الحج والعمرة والسياحة الدينية المعتمدة', description: 'دليل حصري لـ 800 شركة حج وعمرة معتمدة مع بيانات المدراء والمنسقين' }
        ]
    },
    'real-estate': {
        name: 'العقارات',
        icon: 'fas fa-building',
        description: 'قواعد بيانات لقطاع العقارات',
        specializations: [
            { name: 'شركات التطوير العقاري', count: 3200, price: 349, title: 'قاعدة بيانات شركات التطوير العقاري والاستثمار العقاري', description: 'تضم 3200 شركة تطوير عقاري واستثمار عقاري مع بيانات المدراء والمطورين' },
            { name: 'مكاتب الوساطة العقارية', count: 2800, price: 329, title: 'قاعدة بيانات مكاتب الوساطة العقارية والتسويق العقاري', description: 'دليل شامل لـ 2800 مكتب وساطة عقارية مع بيانات الوسطاء والمسوقين العقاريين' },
            { name: 'المستثمرين العقاريين', count: 2200, price: 299, title: 'قاعدة بيانات المستثمرين العقاريين وملاك العقارات الكبار', description: 'تشمل 2200 مستثمر عقاري ومالك عقارات كبير في المشاريع السكنية والتجارية' },
            { name: 'الخبراء العقاريين', count: 1800, price: 279, title: 'قاعدة بيانات الخبراء العقاريين والمثمنين المعتمدين', description: 'دليل متخصص لـ 1800 خبير عقاري ومثمن معتمد في التقييم والاستشارات العقارية' },
            { name: 'شركات العقارات الدولية', count: 2500, price: 339, title: 'قاعدة بيانات شركات العقارات الدولية والاستثمار الأجنبي', description: 'تضم 2500 شركة عقارات دولية ومكتب استثمار أجنبي في المنطقة' }
        ]
    },
    retail: {
        name: 'التجارة والبيع',
        icon: 'fas fa-shopping-cart',
        description: 'قواعد بيانات للقطاع التجاري',
        specializations: [
            { name: 'المتاجر والمحلات التجارية', count: 4500, price: 249, title: 'قاعدة بيانات المتاجر والمحلات التجارية في المولات والأسواق', description: 'أكبر قاعدة بيانات تضم 4500 متجر ومحل تجاري مع بيانات الملاك والمدراء' },
            { name: 'المتاجر الإلكترونية والرقمية', count: 3800, price: 229, title: 'قاعدة بيانات المتاجر الإلكترونية ومنصات التجارة الرقمية', description: 'تشمل 3800 متجر إلكتروني ومنصة تجارة رقمية مع بيانات المؤسسين والمدراء' },
            { name: 'المحلات التجارية المحلية', count: 3200, price: 199, title: 'قاعدة بيانات المحلات التجارية المحلية والأسواق الشعبية', description: 'دليل شامل لـ 3200 محل تجاري محلي في الأسواق والأحياء التجارية' },
            { name: 'سلاسل المحلات والمتاجر', count: 2800, price: 219, title: 'قاعدة بيانات سلاسل المحلات والمتاجر الكبرى والفرانشايز', description: 'تضم 2800 سلسلة محلات ومتجر كبير وفرانشايز مع بيانات الإدارة الإقليمية' },
            { name: 'التجار وأصحاب الأعمال', count: 2200, price: 259, title: 'قاعدة بيانات التجار وأصحاب الأعمال التجارية الصغيرة والمتوسطة', description: 'دليل متخصص لـ 2200 تاجر وصاحب عمل تجاري في مختلف القطاعات' },
            { name: 'قواعد بيانات العملاء والزبائن', count: 8500, price: 299, title: 'قاعدة بيانات شاملة للعملاء والزبائن في القطاعات المختلفة', description: 'أكبر قاعدة بيانات عملاء تضم 8500 عميل وزبون في مختلف القطاعات التجارية' },
            { name: 'قوائم العملاء المستهدفين', count: 6200, price: 279, title: 'قاعدة بيانات العملاء المستهدفين والعملاء المحتملين', description: 'تشمل 6200 عميل مستهدف وعميل محتمل مصنف حسب الاهتمامات والقطاعات' },
            { name: 'قطاع التجزئة والمبيعات', count: 3500, price: 269, title: 'قاعدة بيانات شاملة لقطاع التجزئة والمبيعات والتوزيع', description: 'دليل حصري لـ 3500 جهة في قطاع التجزئة والمبيعات والتوزيع' },
            { name: 'شركات التجزئة الكبرى', count: 2800, price: 249, title: 'قاعدة بيانات شركات التجزئة الكبرى والسوبر ماركت', description: 'تضم 2800 شركة تجزئة كبرى وسوبر ماركت مع بيانات المدراء التنفيذيين' },
            { name: 'تجار الجملة والموزعين', count: 1800, price: 329, title: 'قاعدة بيانات تجار الجملة والموزعين والوكلاء التجاريين', description: 'دليل متخصص لـ 1800 تاجر جملة وموزع ووكيل تجاري معتمد' },
            { name: 'شركات الجملة والتوزيع', count: 1500, price: 299, title: 'قاعدة بيانات شركات الجملة والتوزيع ومراكز التوزيع', description: 'تشمل 1500 شركة جملة وتوزيع ومركز توزيع مع بيانات المدراء' },
            { name: 'المطاعم والمقاهي', count: 2200, price: 199, title: 'قاعدة بيانات المطاعم والمقاهي والكافيهات والمأكولات', description: 'دليل شامل لـ 2200 مطعم ومقهى وكافيه مع بيانات الملاك والمدراء' },
            { name: 'متاجر الملابس والأزياء', count: 1800, price: 179, title: 'قاعدة بيانات متاجر الملابس والأزياء والموضة النسائية والرجالية', description: 'تضم 1800 متجر ملابس وأزياء وموضة مع بيانات الملاك والمصممين' },
            { name: 'محلات الأحذية والإكسسوارات', count: 1200, price: 159, title: 'قاعدة بيانات محلات الأحذية والإكسسوارات والحقائب', description: 'دليل متخصص لـ 1200 محل أحذية وإكسسوارات وحقائب مع بيانات التواصل' },
            { name: 'متاجر مستلزمات المرأة والتجميل', count: 950, price: 169, title: 'قاعدة بيانات متاجر مستلزمات المرأة ومنتجات التجميل والعناية', description: 'تشمل 950 متجر مستلزمات نسائية ومنتجات تجميل وعناية شخصية' }
        ]
    },
    contracting: {
        name: 'المقاولات والإنشاءات',
        icon: 'fas fa-hard-hat',
        description: 'قواعد بيانات لقطاع المقاولات',
        specializations: [
            { name: 'Contracting', count: 2800, price: 349 },
            { name: 'مقاولات', count: 2200, price: 329 },
            { name: 'صيانة', count: 1800, price: 229 },
            { name: 'غيار', count: 1200, price: 179 }
        ]
    },
    telecom: {
        name: 'الاتصالات والتقنية',
        icon: 'fas fa-satellite-dish',
        description: 'قواعد بيانات لقطاع الاتصالات',
        specializations: [
            { name: 'الاتصالات', count: 1800, price: 299 },
            { name: 'Telecom', count: 1500, price: 279 }
        ]
    },
    marketing: {
        name: 'التسويق والإعلان',
        icon: 'fas fa-bullhorn',
        description: 'قواعد بيانات للتسويق والإعلان',
        specializations: [
            { name: 'marketing', count: 3200, price: 329 },
            { name: 'تسويق', count: 2800, price: 309 },
            { name: 'اعلان', count: 1800, price: 249 },
            { name: 'دعاية', count: 1200, price: 219 }
        ]
    },
    agriculture: {
        name: 'الزراعة والثروة الحيوانية',
        icon: 'fas fa-seedling',
        description: 'قواعد بيانات للقطاع الزراعي',
        specializations: [
            { name: 'Agriculture', count: 1500, price: 249 },
            { name: 'الزراعة', count: 1200, price: 229 },
            { name: 'مواشي', count: 800, price: 199 },
            { name: 'الحيوانات', count: 650, price: 179 },
            { name: 'حيوانات', count: 550, price: 169 },
            { name: 'أليفة', count: 400, price: 149 },
            { name: 'دواجن', count: 600, price: 159 },
            { name: 'قطط', count: 300, price: 129 }
        ]
    },
    logistics: {
        name: 'النقل واللوجستيات',
        icon: 'fas fa-truck',
        description: 'قواعد بيانات للنقل واللوجستيات',
        specializations: [
            { name: 'Logistic', count: 1800, price: 279 },
            { name: 'النقل', count: 1500, price: 259 },
            { name: 'shipping', count: 1200, price: 239 },
            { name: 'شحن', count: 1000, price: 219 },
            { name: 'توزيع', count: 800, price: 199 }
        ]
    },
    healthcare: {
        name: 'الرعاية الصحية',
        icon: 'fas fa-heartbeat',
        description: 'قواعد بيانات للرعاية الصحية',
        specializations: [
            { name: 'Healthcare', count: 2200, price: 299 },
            { name: 'Pharmac', count: 1200, price: 349 }
        ]
    },
    services: {
        name: 'الخدمات المتنوعة',
        icon: 'fas fa-concierge-bell',
        description: 'قواعد بيانات للخدمات المختلفة',
        specializations: [
            { name: 'حراسات', count: 1200, price: 199 },
            { name: 'حلاقة', count: 800, price: 149 },
            { name: 'barber', count: 600, price: 139 },
            { name: 'تجميل', count: 950, price: 169 },
            { name: 'كاتب', count: 1500, price: 179 },
            { name: 'تحرير', count: 800, price: 159 },
            { name: 'ترجمة', count: 1200, price: 189 },
            { name: 'صحافة', count: 600, price: 199 },
            { name: 'صحيف', count: 400, price: 179 },
            { name: 'نشر', count: 500, price: 169 },
            { name: 'طباعة', count: 700, price: 149 },
            { name: 'parper', count: 300, price: 129 },
            { name: 'وسيط', count: 800, price: 199 },
            { name: 'لاعب', count: 400, price: 159 }
        ]
    },
    renewable: {
        name: 'الطاقة المتجددة',
        icon: 'fas fa-solar-panel',
        description: 'قواعد بيانات للطاقة المتجددة',
        specializations: [
            { name: 'Renewable', count: 600, price: 349 }
        ]
    }
};

// Products data (sample data for demonstration)
let productsData = [];

// Use global variables from database-main.js
// currentFilters and currentPage are defined in database-main.js
let itemsPerPage = 12;

// Load categories
function loadCategories() {
    const categoriesGrid = document.getElementById('categoriesGrid');
    if (!categoriesGrid) return;

    // Get categories data from data loader
    const categories = window.DatabaseDataLoader ?
        window.DatabaseDataLoader.getAllCategories() :
        legacyCategoriesData;

    categoriesGrid.innerHTML = '';

    Object.entries(categories).forEach(([key, category]) => {
        const totalCount = category.specializations.reduce((sum, spec) => sum + spec.count, 0);
        const avgPrice = Math.round(category.specializations.reduce((sum, spec) => sum + spec.price, 0) / category.specializations.length);

        const categoryCard = document.createElement('div');
        categoryCard.className = 'category-card';
        categoryCard.onclick = () => filterByCategory(key);

        categoryCard.innerHTML = `
            <div class="category-icon">
                <i class="${category.icon}"></i>
            </div>
            <h3 class="category-title">${category.name}</h3>
            <p class="category-description">${category.description}</p>
            <div class="category-stats">
                <div class="category-count">${formatNumber(totalCount)} جهة اتصال</div>
                <div class="category-price">من ${formatPrice(Math.min(...category.specializations.map(s => s.price)))}</div>
            </div>
        `;

        categoriesGrid.appendChild(categoryCard);
    });
}

// Load products
function loadProducts() {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    // Get products data from data loader
    if (window.DatabaseDataLoader && window.DatabaseDataLoader.isDataLoaded()) {
        productsData = window.DatabaseDataLoader.productsData();
    } else {
        // Fallback: generate products from legacy data
        generateProductsData();
    }

    // Get current filters from global scope
    const filters = window.currentFilters || {
        search: '',
        category: '',
        priceRange: '',
        sort: 'relevance'
    };

    // Filter products based on current filters
    let filteredProducts = window.DatabaseDataLoader ?
        window.DatabaseDataLoader.filterProducts(filters) :
        filterProductsLegacy(productsData);

    // Pagination
    const currentPageNum = window.currentPage || 1;
    const startIndex = (currentPageNum - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    // Show loading skeleton first
    showProductsSkeleton(productsGrid);

    // Simulate loading delay
    setTimeout(() => {
        renderProducts(productsGrid, paginatedProducts);
        updateLoadMoreButton(filteredProducts.length);
    }, 500);
}

function generateProductsData() {
    productsData = [];

    // Get categories data from data loader or use legacy data
    const categories = window.DatabaseDataLoader ?
        window.DatabaseDataLoader.getAllCategories() :
        legacyCategoriesData;

    Object.entries(categories).forEach(([categoryKey, category]) => {
        category.specializations.forEach(spec => {
            const product = {
                id: spec.id || `${categoryKey}-${spec.name.replace(/\s+/g, '-')}`,
                title: spec.title || `قاعدة بيانات ${spec.name}`,
                category: category.name,
                categoryKey: categoryKey,
                description: spec.description || `قاعدة بيانات شاملة ومحدثة لـ ${spec.name} في المنطقة الخليجية والعربية`,
                count: spec.count,
                price: spec.price,
                originalPrice: Math.round(spec.price * 1.3),
                discount: 23,
                features: [
                    'بيانات محدثة ومؤكدة خلال آخر 30 يوم',
                    'تغطية شاملة للمنطقة الخليجية والعربية',
                    'تصدير بصيغ متعددة: Excel, CSV, JSON, PDF',
                    'دعم فني مجاني على مدار الساعة',
                    'ضمان استرداد المال خلال 7 أيام',
                    'بيانات مؤكدة ومفلترة من المصادر الموثوقة',
                    'تحديثات دورية مجانية لمدة 3 أشهر'
                ],
                rating: (4.2 + Math.random() * 0.8).toFixed(1),
                reviews: Math.floor(Math.random() * 100) + 20,
                specialization: spec.name,
                locations: spec.locations || [],
                specialties: spec.specialties || [],
                types: spec.types || [],
                categories: spec.categories || [],
                services: spec.services || [],
                sectors: spec.sectors || []
            };
            productsData.push(product);
        });
    });
}

function filterProductsLegacy(products) {
    const filters = window.currentFilters || {};

    return products.filter(product => {
        // Search filter
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            if (!product.title.toLowerCase().includes(searchTerm) &&
                !product.category.toLowerCase().includes(searchTerm)) {
                return false;
            }
        }

        // Category filter
        if (filters.category && product.categoryKey !== filters.category) {
            return false;
        }

        // Price filter
        if (filters.priceRange) {
            const [min, max] = filters.priceRange.split('-').map(p => p === '+' ? Infinity : parseInt(p));
            if (product.price < min || (max !== Infinity && product.price > max)) {
                return false;
            }
        }

        return true;
    });
}

function showProductsSkeleton(container) {
    container.innerHTML = '';
    
    for (let i = 0; i < itemsPerPage; i++) {
        const skeleton = document.createElement('div');
        skeleton.className = 'skeleton-card';
        skeleton.innerHTML = `
            <div class="skeleton skeleton-header"></div>
            <div class="skeleton skeleton-title"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text short"></div>
            <div class="skeleton skeleton-button"></div>
        `;
        container.appendChild(skeleton);
    }
}

function renderProducts(container, products) {
    container.innerHTML = '';

    if (products.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h3>لم يتم العثور على نتائج</h3>
                <p class="text-muted">جرب تغيير معايير البحث أو الفلترة</p>
            </div>
        `;
        return;
    }

    products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';

        productCard.innerHTML = `
            <div class="product-header">
                <div class="product-badge">خصم ${product.discount}%</div>
                <h3 class="product-title">${product.title}</h3>
                <p class="product-category">${product.category}</p>
            </div>
            <div class="product-body">
                <p class="product-description">${product.description}</p>
                <ul class="product-features">
                    ${product.features.map(feature => `
                        <li><i class="fas fa-check"></i> ${feature}</li>
                    `).join('')}
                </ul>
                <div class="product-stats">
                    <div class="stat-box">
                        <div class="stat-value">${formatNumber(product.count)}</div>
                        <div class="stat-label">جهة اتصال</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">${product.rating}</div>
                        <div class="stat-label">التقييم</div>
                    </div>
                </div>
            </div>
            <div class="product-footer">
                <div class="product-price">
                    <div>
                        <span class="price-current">${formatPrice(product.price)}</span>
                        <span class="price-original">${formatPrice(product.originalPrice)}</span>
                    </div>
                    <div class="price-discount">وفر ${formatPrice(product.originalPrice - product.price)}</div>
                </div>
                <div class="product-actions">
                    <button class="btn-add-cart" onclick="addToCart('${product.id}')">
                        <i class="fas fa-cart-plus me-2"></i>
                        أضف للسلة
                    </button>
                    <button class="btn-view-details" onclick="viewProductDetails('${product.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(productCard);
    });
}

function updateLoadMoreButton(totalProducts) {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;

    const totalPages = Math.ceil(totalProducts / itemsPerPage);
    
    if (currentPage >= totalPages) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.onclick = loadMoreProducts;
    }
}

function loadMoreProducts() {
    currentPage++;
    loadProducts();
}

function filterByCategory(categoryKey) {
    currentFilters.category = categoryKey;
    currentPage = 1;
    
    // Update category filter dropdown
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.value = categoryKey;
    }
    
    loadProducts();
    scrollToSection('products');
}

function viewProductDetails(productId) {
    const product = productsData.find(p => p.id === productId);
    if (!product) return;

    // Update modal title
    const modalTitle = document.getElementById('productModalTitle');
    if (modalTitle) {
        modalTitle.textContent = product.title;
    }

    // Render product details
    renderProductDetails(product);

    // Show modal
    const productModal = new bootstrap.Modal(document.getElementById('productModal'));
    productModal.show();
}

function renderProductDetails(product) {
    const modalBody = document.getElementById('productModalBody');
    if (!modalBody) return;

    const categoryData = Object.values(categoriesData).find(cat =>
        cat.specializations.some(spec => spec.name === product.title.replace('قاعدة بيانات ', ''))
    );

    modalBody.innerHTML = `
        <div class="product-details">
            <div class="row">
                <div class="col-lg-8">
                    <div class="product-info">
                        <h3>${product.title}</h3>
                        <p class="text-muted mb-3">${product.category}</p>
                        <p class="product-description">${product.description}</p>

                        <div class="product-highlights mb-4">
                            <h5>مميزات قاعدة البيانات:</h5>
                            <ul class="list-unstyled">
                                ${product.features.map(feature => `
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        ${feature}
                                    </li>
                                `).join('')}
                            </ul>
                        </div>

                        <div class="product-specs">
                            <h5 class="mb-3">مواصفات قاعدة البيانات:</h5>
                            <div class="spec-item">
                                <span class="spec-label">عدد جهات الاتصال:</span>
                                <span class="spec-value">${formatNumber(product.count)} جهة</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">التغطية الجغرافية:</span>
                                <span class="spec-value">دول الخليج والمنطقة العربية</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">صيغ التصدير:</span>
                                <span class="spec-value">Excel, CSV, JSON, PDF</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">آخر تحديث:</span>
                                <span class="spec-value">خلال آخر 30 يوم</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">نسبة الدقة:</span>
                                <span class="spec-value">99%</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">التقييم:</span>
                                <span class="spec-value">
                                    ${product.rating}
                                    <span class="text-warning">
                                        ${'★'.repeat(Math.floor(product.rating))}
                                    </span>
                                    (${product.reviews} تقييم)
                                </span>
                            </div>
                        </div>

                        <div class="data-sample">
                            <h5 class="mb-3">عينة من البيانات:</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>المسمى الوظيفي</th>
                                            <th>الشركة</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الهاتف</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>أحمد محمد</td>
                                            <td>${product.title.replace('قاعدة بيانات ', '')}</td>
                                            <td>شركة النجاح</td>
                                            <td><EMAIL></td>
                                            <td>+966501234567</td>
                                        </tr>
                                        <tr>
                                            <td>فاطمة علي</td>
                                            <td>${product.title.replace('قاعدة بيانات ', '')}</td>
                                            <td>مؤسسة التميز</td>
                                            <td><EMAIL></td>
                                            <td>+966507654321</td>
                                        </tr>
                                        <tr>
                                            <td>خالد السعيد</td>
                                            <td>${product.title.replace('قاعدة بيانات ', '')}</td>
                                            <td>مجموعة الرائد</td>
                                            <td><EMAIL></td>
                                            <td>+966509876543</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                هذه عينة توضيحية فقط. البيانات الفعلية أكثر تفصيلاً وشمولية.
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="product-purchase-card">
                        <div class="price-section mb-4">
                            <div class="current-price">
                                <span class="price-label">السعر الحالي:</span>
                                <span class="price-value">${formatPrice(product.price)}</span>
                            </div>
                            <div class="original-price">
                                <span class="text-muted text-decoration-line-through">${formatPrice(product.originalPrice)}</span>
                                <span class="discount-badge">خصم ${product.discount}%</span>
                            </div>
                            <div class="savings">
                                <small class="text-success">
                                    <i class="fas fa-tag me-1"></i>
                                    وفر ${formatPrice(product.originalPrice - product.price)}
                                </small>
                            </div>
                        </div>

                        <div class="purchase-options mb-4">
                            <div class="quantity-selector mb-3">
                                <label class="form-label">الكمية:</label>
                                <div class="input-group">
                                    <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(-1)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" class="form-control text-center" id="modalQuantity" value="1" min="1" max="10">
                                    <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(1)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="total-price mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>المجموع:</span>
                                    <span class="fw-bold" id="modalTotalPrice">${formatPrice(product.price)}</span>
                                </div>
                            </div>
                        </div>

                        <div class="purchase-actions">
                            <button class="btn btn-primary btn-lg w-100 mb-2" onclick="addToCartFromModal('${product.id}')">
                                <i class="fas fa-cart-plus me-2"></i>
                                أضف إلى السلة
                            </button>
                            <button class="btn btn-success btn-lg w-100 mb-3" onclick="buyNowFromModal('${product.id}')">
                                <i class="fas fa-bolt me-2"></i>
                                اشتري الآن
                            </button>
                        </div>

                        <div class="security-badges">
                            <div class="badge-item">
                                <i class="fas fa-shield-alt text-success"></i>
                                <span>دفع آمن 100%</span>
                            </div>
                            <div class="badge-item">
                                <i class="fas fa-undo text-info"></i>
                                <span>ضمان استرداد المال</span>
                            </div>
                            <div class="badge-item">
                                <i class="fas fa-download text-primary"></i>
                                <span>تحميل فوري</span>
                            </div>
                        </div>
                    </div>

                    <div class="related-products mt-4">
                        <h6>منتجات ذات صلة:</h6>
                        <div class="related-list">
                            ${getRelatedProducts(product, 3).map(related => `
                                <div class="related-item" onclick="viewProductDetails('${related.id}')">
                                    <div class="related-info">
                                        <div class="related-title">${related.title}</div>
                                        <div class="related-price">${formatPrice(related.price)}</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add CSS for product purchase card
    addProductModalCSS();
}

function getRelatedProducts(currentProduct, count = 3) {
    return productsData
        .filter(p => p.categoryKey === currentProduct.categoryKey && p.id !== currentProduct.id)
        .slice(0, count);
}

function changeQuantity(change) {
    const quantityInput = document.getElementById('modalQuantity');
    const totalPriceElement = document.getElementById('modalTotalPrice');

    if (!quantityInput || !totalPriceElement) return;

    let currentQuantity = parseInt(quantityInput.value) || 1;
    let newQuantity = currentQuantity + change;

    if (newQuantity < 1) newQuantity = 1;
    if (newQuantity > 10) newQuantity = 10;

    quantityInput.value = newQuantity;

    // Update total price
    const productId = document.querySelector('[onclick*="addToCartFromModal"]').getAttribute('onclick').match(/'([^']+)'/)[1];
    const product = productsData.find(p => p.id === productId);

    if (product) {
        const totalPrice = product.price * newQuantity;
        totalPriceElement.textContent = formatPrice(totalPrice);
    }
}

function addToCartFromModal(productId) {
    const quantity = parseInt(document.getElementById('modalQuantity').value) || 1;

    for (let i = 0; i < quantity; i++) {
        addToCart(productId);
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    if (modal) modal.hide();
}

function buyNowFromModal(productId) {
    const quantity = parseInt(document.getElementById('modalQuantity').value) || 1;

    // Add to cart
    for (let i = 0; i < quantity; i++) {
        addToCart(productId);
    }

    // Close modal and proceed to checkout
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    if (modal) modal.hide();

    setTimeout(() => {
        proceedToCheckout();
    }, 500);
}

function addProductModalCSS() {
    if (document.getElementById('productModalCSS')) return;

    const style = document.createElement('style');
    style.id = 'productModalCSS';
    style.textContent = `
        .product-purchase-card {
            background: var(--gray-100);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            position: sticky;
            top: 2rem;
        }

        .price-section {
            text-align: center;
            border-bottom: 1px solid var(--gray-300);
            padding-bottom: 1rem;
        }

        .current-price {
            margin-bottom: 0.5rem;
        }

        .price-label {
            display: block;
            font-size: 0.9rem;
            color: var(--gray-600);
            margin-bottom: 0.25rem;
        }

        .price-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .original-price {
            margin-bottom: 0.5rem;
        }

        .discount-badge {
            background: var(--success-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius);
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }

        .security-badges {
            border-top: 1px solid var(--gray-300);
            padding-top: 1rem;
        }

        .badge-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: var(--gray-700);
        }

        .related-products {
            background: white;
            border-radius: var(--border-radius);
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .related-item {
            padding: 0.75rem;
            border-bottom: 1px solid var(--gray-200);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .related-item:hover {
            background: var(--gray-100);
        }

        .related-item:last-child {
            border-bottom: none;
        }

        .related-title {
            font-size: 0.9rem;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
        }

        .related-price {
            font-size: 0.8rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        .data-sample {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            border: 1px solid var(--gray-200);
            margin-bottom: 2rem;
        }

        .table th {
            background: var(--gray-100);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table td {
            font-size: 0.9rem;
        }
    `;

    document.head.appendChild(style);
}

// Initialize categories when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add load more button event listener
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreProducts);
    }
});

// Filter by category
function filterByCategory(categoryKey) {
    // Update global filters
    if (window.currentFilters) {
        window.currentFilters.category = categoryKey;
    }
    if (window.currentPage !== undefined) {
        window.currentPage = 1;
    }

    loadProducts();

    // Update URL hash
    window.location.hash = `category=${categoryKey}`;

    // Scroll to products section
    const productsSection = document.getElementById('products');
    if (productsSection) {
        productsSection.scrollIntoView({ behavior: 'smooth' });
    }
}

// Clear filters
function clearFilters() {
    // Reset global filters
    if (window.currentFilters) {
        window.currentFilters = {
            search: '',
            category: '',
            priceRange: '',
            sort: 'relevance'
        };
    }
    if (window.currentPage !== undefined) {
        window.currentPage = 1;
    }

    loadProducts();

    // Clear URL hash
    window.location.hash = '';
}

// Update load more button
function updateLoadMoreButton(totalFilteredProducts) {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;

    const currentPageNum = window.currentPage || 1;
    const totalShown = currentPageNum * itemsPerPage;
    if (totalShown >= totalFilteredProducts) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.textContent = `عرض المزيد (${totalFilteredProducts - totalShown} متبقي)`;
    }
}

// Load more products
function loadMoreProducts() {
    if (window.currentPage !== undefined) {
        window.currentPage++;
    }
    loadProducts();
}

// Export functions
window.DatabaseCategories = {
    loadCategories,
    loadProducts,
    filterByCategory,
    clearFilters,
    viewProductDetails,
    loadMoreProducts,
    categoriesData: () => window.DatabaseDataLoader ? window.DatabaseDataLoader.getAllCategories() : legacyCategoriesData,
    productsData: () => productsData
};
