// Database Categories and Specializations

// Categories data structure
const categoriesData = {
    medical: {
        name: 'الطبية والصحية',
        icon: 'fas fa-user-md',
        description: 'قواعد بيانات للمهن الطبية والصحية',
        specializations: [
            { name: 'طبيب', count: 2500, price: 299 },
            { name: 'مستشفى', count: 450, price: 199 },
            { name: 'عيادة', count: 1200, price: 149 },
            { name: 'تمريض', count: 3200, price: 179 },
            { name: 'العلاج الطبيعي', count: 800, price: 129 },
            { name: 'العلاج', count: 1500, price: 159 }
        ]
    },
    engineering: {
        name: 'الهندسة والتقنية',
        icon: 'fas fa-cogs',
        description: 'قواعد بيانات للمهندسين والتقنيين',
        specializations: [
            { name: 'مهندسين', count: 4200, price: 349 },
            { name: 'مهندس', count: 3800, price: 329 },
            { name: 'Industrial', count: 1600, price: 249 },
            { name: 'Petrochemicals', count: 900, price: 399 },
            { name: 'Energy', count: 1200, price: 279 },
            { name: 'Oil', count: 800, price: 459 },
            { name: 'gas', count: 600, price: 389 },
            { name: 'Mining', count: 400, price: 299 },
            { name: 'التعدين', count: 350, price: 279 }
        ]
    },
    business: {
        name: 'الأعمال والإدارة',
        icon: 'fas fa-briefcase',
        description: 'قواعد بيانات لرجال الأعمال والإدارة',
        specializations: [
            { name: 'manager', count: 5200, price: 399 },
            { name: 'مدير فندق', count: 320, price: 199 },
            { name: 'إدارة الأعمال', count: 2800, price: 349 },
            { name: 'إدارة', count: 3200, price: 329 },
            { name: 'الإدارة', count: 2900, price: 319 },
            { name: 'CEO', count: 450, price: 599 },
            { name: 'Entrepreneur', count: 800, price: 449 },
            { name: 'رائد اعمال', count: 650, price: 429 },
            { name: 'رائد اعمال2', count: 420, price: 399 },
            { name: 'company', count: 6500, price: 499 },
            { name: 'مؤسسة', count: 4200, price: 379 },
            { name: 'institution', count: 2800, price: 299 }
        ]
    },
    education: {
        name: 'التعليم والتدريب',
        icon: 'fas fa-graduation-cap',
        description: 'قواعد بيانات للمؤسسات التعليمية',
        specializations: [
            { name: 'مدرس', count: 8500, price: 249 },
            { name: 'مدارس', count: 1200, price: 199 },
            { name: 'مدرسة', count: 2800, price: 179 },
            { name: 'جمعية', count: 450, price: 149 },
            { name: 'توظيف', count: 1800, price: 229 }
        ]
    },
    finance: {
        name: 'المالية والمصرفية',
        icon: 'fas fa-university',
        description: 'قواعد بيانات للقطاع المالي والمصرفي',
        specializations: [
            { name: 'بنك', count: 850, price: 399 },
            { name: 'يعمل في بنك', count: 3200, price: 349 },
            { name: 'Bank', count: 1200, price: 379 },
            { name: 'مصرف', count: 650, price: 359 },
            { name: 'محاسب', count: 4500, price: 299 },
            { name: 'Financia', count: 1800, price: 329 },
            { name: 'تأمين', count: 1200, price: 249 },
            { name: 'صراف', count: 800, price: 179 },
            { name: 'صرافة', count: 450, price: 199 }
        ]
    },
    tourism: {
        name: 'السياحة والسفر',
        icon: 'fas fa-plane',
        description: 'قواعد بيانات لقطاع السياحة والسفر',
        specializations: [
            { name: 'يعمل في السياحة والسفر', count: 2200, price: 299 },
            { name: 'Tourism', count: 1800, price: 279 },
            { name: 'السياحة', count: 1500, price: 259 },
            { name: 'السفر', count: 1200, price: 239 },
            { name: 'travel', count: 1600, price: 269 },
            { name: 'hotel', count: 950, price: 219 },
            { name: 'فنادق', count: 1100, price: 229 },
            { name: 'الحج', count: 800, price: 199 }
        ]
    },
    'real-estate': {
        name: 'العقارات',
        icon: 'fas fa-building',
        description: 'قواعد بيانات لقطاع العقارات',
        specializations: [
            { name: 'عقارات', count: 3200, price: 349 },
            { name: 'العقارات', count: 2800, price: 329 },
            { name: 'عقار', count: 2200, price: 299 },
            { name: 'عقاري', count: 1800, price: 279 },
            { name: 'Real Estate', count: 2500, price: 339 }
        ]
    },
    retail: {
        name: 'التجارة والبيع',
        icon: 'fas fa-shopping-cart',
        description: 'قواعد بيانات للقطاع التجاري',
        specializations: [
            { name: 'متجر', count: 4500, price: 249 },
            { name: 'store', count: 3800, price: 229 },
            { name: 'محل', count: 3200, price: 199 },
            { name: 'محلات', count: 2800, price: 219 },
            { name: 'تاجر', count: 2200, price: 259 },
            { name: 'customer', count: 8500, price: 299 },
            { name: 'عملاء', count: 6200, price: 279 },
            { name: 'Retail', count: 3500, price: 269 },
            { name: 'التجزئة', count: 2800, price: 249 },
            { name: 'Wholesale', count: 1800, price: 329 },
            { name: 'جملة', count: 1500, price: 299 },
            { name: 'restaurant', count: 2200, price: 199 },
            { name: 'ملابس', count: 1800, price: 179 },
            { name: 'احذية', count: 1200, price: 159 },
            { name: 'مستلزمات المرأة', count: 950, price: 169 }
        ]
    },
    contracting: {
        name: 'المقاولات والإنشاءات',
        icon: 'fas fa-hard-hat',
        description: 'قواعد بيانات لقطاع المقاولات',
        specializations: [
            { name: 'Contracting', count: 2800, price: 349 },
            { name: 'مقاولات', count: 2200, price: 329 },
            { name: 'صيانة', count: 1800, price: 229 },
            { name: 'غيار', count: 1200, price: 179 }
        ]
    },
    telecom: {
        name: 'الاتصالات والتقنية',
        icon: 'fas fa-satellite-dish',
        description: 'قواعد بيانات لقطاع الاتصالات',
        specializations: [
            { name: 'الاتصالات', count: 1800, price: 299 },
            { name: 'Telecom', count: 1500, price: 279 }
        ]
    },
    marketing: {
        name: 'التسويق والإعلان',
        icon: 'fas fa-bullhorn',
        description: 'قواعد بيانات للتسويق والإعلان',
        specializations: [
            { name: 'marketing', count: 3200, price: 329 },
            { name: 'تسويق', count: 2800, price: 309 },
            { name: 'اعلان', count: 1800, price: 249 },
            { name: 'دعاية', count: 1200, price: 219 }
        ]
    },
    agriculture: {
        name: 'الزراعة والثروة الحيوانية',
        icon: 'fas fa-seedling',
        description: 'قواعد بيانات للقطاع الزراعي',
        specializations: [
            { name: 'Agriculture', count: 1500, price: 249 },
            { name: 'الزراعة', count: 1200, price: 229 },
            { name: 'مواشي', count: 800, price: 199 },
            { name: 'الحيوانات', count: 650, price: 179 },
            { name: 'حيوانات', count: 550, price: 169 },
            { name: 'أليفة', count: 400, price: 149 },
            { name: 'دواجن', count: 600, price: 159 },
            { name: 'قطط', count: 300, price: 129 }
        ]
    },
    logistics: {
        name: 'النقل واللوجستيات',
        icon: 'fas fa-truck',
        description: 'قواعد بيانات للنقل واللوجستيات',
        specializations: [
            { name: 'Logistic', count: 1800, price: 279 },
            { name: 'النقل', count: 1500, price: 259 },
            { name: 'shipping', count: 1200, price: 239 },
            { name: 'شحن', count: 1000, price: 219 },
            { name: 'توزيع', count: 800, price: 199 }
        ]
    },
    healthcare: {
        name: 'الرعاية الصحية',
        icon: 'fas fa-heartbeat',
        description: 'قواعد بيانات للرعاية الصحية',
        specializations: [
            { name: 'Healthcare', count: 2200, price: 299 },
            { name: 'Pharmac', count: 1200, price: 349 }
        ]
    },
    services: {
        name: 'الخدمات المتنوعة',
        icon: 'fas fa-concierge-bell',
        description: 'قواعد بيانات للخدمات المختلفة',
        specializations: [
            { name: 'حراسات', count: 1200, price: 199 },
            { name: 'حلاقة', count: 800, price: 149 },
            { name: 'barber', count: 600, price: 139 },
            { name: 'تجميل', count: 950, price: 169 },
            { name: 'كاتب', count: 1500, price: 179 },
            { name: 'تحرير', count: 800, price: 159 },
            { name: 'ترجمة', count: 1200, price: 189 },
            { name: 'صحافة', count: 600, price: 199 },
            { name: 'صحيف', count: 400, price: 179 },
            { name: 'نشر', count: 500, price: 169 },
            { name: 'طباعة', count: 700, price: 149 },
            { name: 'parper', count: 300, price: 129 },
            { name: 'وسيط', count: 800, price: 199 },
            { name: 'لاعب', count: 400, price: 159 }
        ]
    },
    renewable: {
        name: 'الطاقة المتجددة',
        icon: 'fas fa-solar-panel',
        description: 'قواعد بيانات للطاقة المتجددة',
        specializations: [
            { name: 'Renewable', count: 600, price: 349 }
        ]
    }
};

// Products data (sample data for demonstration)
let productsData = [];

// Load categories
function loadCategories() {
    const categoriesGrid = document.getElementById('categoriesGrid');
    if (!categoriesGrid) return;

    categoriesGrid.innerHTML = '';

    Object.entries(categoriesData).forEach(([key, category]) => {
        const totalCount = category.specializations.reduce((sum, spec) => sum + spec.count, 0);
        const avgPrice = Math.round(category.specializations.reduce((sum, spec) => sum + spec.price, 0) / category.specializations.length);

        const categoryCard = document.createElement('div');
        categoryCard.className = 'category-card';
        categoryCard.onclick = () => filterByCategory(key);

        categoryCard.innerHTML = `
            <div class="category-icon">
                <i class="${category.icon}"></i>
            </div>
            <h3 class="category-title">${category.name}</h3>
            <p class="category-description">${category.description}</p>
            <div class="category-stats">
                <div class="category-count">${formatNumber(totalCount)} جهة اتصال</div>
                <div class="category-price">من ${formatPrice(Math.min(...category.specializations.map(s => s.price)))}</div>
            </div>
        `;

        categoriesGrid.appendChild(categoryCard);
    });
}

// Load products
function loadProducts() {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    // Generate products from categories data
    generateProductsData();

    // Filter products based on current filters
    let filteredProducts = filterProducts(productsData);

    // Pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    // Show loading skeleton first
    showProductsSkeleton(productsGrid);

    // Simulate loading delay
    setTimeout(() => {
        renderProducts(productsGrid, paginatedProducts);
        updateLoadMoreButton(filteredProducts.length);
    }, 500);
}

function generateProductsData() {
    productsData = [];
    
    Object.entries(categoriesData).forEach(([categoryKey, category]) => {
        category.specializations.forEach(spec => {
            const product = {
                id: `${categoryKey}-${spec.name.replace(/\s+/g, '-')}`,
                title: `قاعدة بيانات ${spec.name}`,
                category: category.name,
                categoryKey: categoryKey,
                description: `قاعدة بيانات شاملة ومحدثة لـ ${spec.name} في المنطقة الخليجية والعربية`,
                count: spec.count,
                price: spec.price,
                originalPrice: Math.round(spec.price * 1.3),
                discount: 23,
                features: [
                    'بيانات محدثة ومؤكدة',
                    'تغطية شاملة للمنطقة',
                    'تصدير بصيغ متعددة',
                    'دعم فني مجاني',
                    'ضمان استرداد المال'
                ],
                rating: (4.2 + Math.random() * 0.8).toFixed(1),
                reviews: Math.floor(Math.random() * 100) + 20
            };
            productsData.push(product);
        });
    });
}

function filterProducts(products) {
    return products.filter(product => {
        // Search filter
        if (currentFilters.search) {
            const searchTerm = currentFilters.search.toLowerCase();
            if (!product.title.toLowerCase().includes(searchTerm) &&
                !product.category.toLowerCase().includes(searchTerm)) {
                return false;
            }
        }

        // Category filter
        if (currentFilters.category && product.categoryKey !== currentFilters.category) {
            return false;
        }

        // Price filter
        if (currentFilters.priceRange) {
            const [min, max] = currentFilters.priceRange.split('-').map(p => p === '+' ? Infinity : parseInt(p));
            if (product.price < min || (max !== Infinity && product.price > max)) {
                return false;
            }
        }

        return true;
    });
}

function showProductsSkeleton(container) {
    container.innerHTML = '';
    
    for (let i = 0; i < itemsPerPage; i++) {
        const skeleton = document.createElement('div');
        skeleton.className = 'skeleton-card';
        skeleton.innerHTML = `
            <div class="skeleton skeleton-header"></div>
            <div class="skeleton skeleton-title"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text short"></div>
            <div class="skeleton skeleton-button"></div>
        `;
        container.appendChild(skeleton);
    }
}

function renderProducts(container, products) {
    container.innerHTML = '';

    if (products.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h3>لم يتم العثور على نتائج</h3>
                <p class="text-muted">جرب تغيير معايير البحث أو الفلترة</p>
            </div>
        `;
        return;
    }

    products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';

        productCard.innerHTML = `
            <div class="product-header">
                <div class="product-badge">خصم ${product.discount}%</div>
                <h3 class="product-title">${product.title}</h3>
                <p class="product-category">${product.category}</p>
            </div>
            <div class="product-body">
                <p class="product-description">${product.description}</p>
                <ul class="product-features">
                    ${product.features.map(feature => `
                        <li><i class="fas fa-check"></i> ${feature}</li>
                    `).join('')}
                </ul>
                <div class="product-stats">
                    <div class="stat-box">
                        <div class="stat-value">${formatNumber(product.count)}</div>
                        <div class="stat-label">جهة اتصال</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">${product.rating}</div>
                        <div class="stat-label">التقييم</div>
                    </div>
                </div>
            </div>
            <div class="product-footer">
                <div class="product-price">
                    <div>
                        <span class="price-current">${formatPrice(product.price)}</span>
                        <span class="price-original">${formatPrice(product.originalPrice)}</span>
                    </div>
                    <div class="price-discount">وفر ${formatPrice(product.originalPrice - product.price)}</div>
                </div>
                <div class="product-actions">
                    <button class="btn-add-cart" onclick="addToCart('${product.id}')">
                        <i class="fas fa-cart-plus me-2"></i>
                        أضف للسلة
                    </button>
                    <button class="btn-view-details" onclick="viewProductDetails('${product.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(productCard);
    });
}

function updateLoadMoreButton(totalProducts) {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;

    const totalPages = Math.ceil(totalProducts / itemsPerPage);
    
    if (currentPage >= totalPages) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.onclick = loadMoreProducts;
    }
}

function loadMoreProducts() {
    currentPage++;
    loadProducts();
}

function filterByCategory(categoryKey) {
    currentFilters.category = categoryKey;
    currentPage = 1;
    
    // Update category filter dropdown
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.value = categoryKey;
    }
    
    loadProducts();
    scrollToSection('products');
}

function viewProductDetails(productId) {
    const product = productsData.find(p => p.id === productId);
    if (!product) return;

    // Update modal title
    const modalTitle = document.getElementById('productModalTitle');
    if (modalTitle) {
        modalTitle.textContent = product.title;
    }

    // Render product details
    renderProductDetails(product);

    // Show modal
    const productModal = new bootstrap.Modal(document.getElementById('productModal'));
    productModal.show();
}

function renderProductDetails(product) {
    const modalBody = document.getElementById('productModalBody');
    if (!modalBody) return;

    const categoryData = Object.values(categoriesData).find(cat =>
        cat.specializations.some(spec => spec.name === product.title.replace('قاعدة بيانات ', ''))
    );

    modalBody.innerHTML = `
        <div class="product-details">
            <div class="row">
                <div class="col-lg-8">
                    <div class="product-info">
                        <h3>${product.title}</h3>
                        <p class="text-muted mb-3">${product.category}</p>
                        <p class="product-description">${product.description}</p>

                        <div class="product-highlights mb-4">
                            <h5>مميزات قاعدة البيانات:</h5>
                            <ul class="list-unstyled">
                                ${product.features.map(feature => `
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        ${feature}
                                    </li>
                                `).join('')}
                            </ul>
                        </div>

                        <div class="product-specs">
                            <h5 class="mb-3">مواصفات قاعدة البيانات:</h5>
                            <div class="spec-item">
                                <span class="spec-label">عدد جهات الاتصال:</span>
                                <span class="spec-value">${formatNumber(product.count)} جهة</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">التغطية الجغرافية:</span>
                                <span class="spec-value">دول الخليج والمنطقة العربية</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">صيغ التصدير:</span>
                                <span class="spec-value">Excel, CSV, JSON, PDF</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">آخر تحديث:</span>
                                <span class="spec-value">خلال آخر 30 يوم</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">نسبة الدقة:</span>
                                <span class="spec-value">99%</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">التقييم:</span>
                                <span class="spec-value">
                                    ${product.rating}
                                    <span class="text-warning">
                                        ${'★'.repeat(Math.floor(product.rating))}
                                    </span>
                                    (${product.reviews} تقييم)
                                </span>
                            </div>
                        </div>

                        <div class="data-sample">
                            <h5 class="mb-3">عينة من البيانات:</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>المسمى الوظيفي</th>
                                            <th>الشركة</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الهاتف</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>أحمد محمد</td>
                                            <td>${product.title.replace('قاعدة بيانات ', '')}</td>
                                            <td>شركة النجاح</td>
                                            <td><EMAIL></td>
                                            <td>+966501234567</td>
                                        </tr>
                                        <tr>
                                            <td>فاطمة علي</td>
                                            <td>${product.title.replace('قاعدة بيانات ', '')}</td>
                                            <td>مؤسسة التميز</td>
                                            <td><EMAIL></td>
                                            <td>+966507654321</td>
                                        </tr>
                                        <tr>
                                            <td>خالد السعيد</td>
                                            <td>${product.title.replace('قاعدة بيانات ', '')}</td>
                                            <td>مجموعة الرائد</td>
                                            <td><EMAIL></td>
                                            <td>+966509876543</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                هذه عينة توضيحية فقط. البيانات الفعلية أكثر تفصيلاً وشمولية.
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="product-purchase-card">
                        <div class="price-section mb-4">
                            <div class="current-price">
                                <span class="price-label">السعر الحالي:</span>
                                <span class="price-value">${formatPrice(product.price)}</span>
                            </div>
                            <div class="original-price">
                                <span class="text-muted text-decoration-line-through">${formatPrice(product.originalPrice)}</span>
                                <span class="discount-badge">خصم ${product.discount}%</span>
                            </div>
                            <div class="savings">
                                <small class="text-success">
                                    <i class="fas fa-tag me-1"></i>
                                    وفر ${formatPrice(product.originalPrice - product.price)}
                                </small>
                            </div>
                        </div>

                        <div class="purchase-options mb-4">
                            <div class="quantity-selector mb-3">
                                <label class="form-label">الكمية:</label>
                                <div class="input-group">
                                    <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(-1)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" class="form-control text-center" id="modalQuantity" value="1" min="1" max="10">
                                    <button class="btn btn-outline-secondary" type="button" onclick="changeQuantity(1)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="total-price mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>المجموع:</span>
                                    <span class="fw-bold" id="modalTotalPrice">${formatPrice(product.price)}</span>
                                </div>
                            </div>
                        </div>

                        <div class="purchase-actions">
                            <button class="btn btn-primary btn-lg w-100 mb-2" onclick="addToCartFromModal('${product.id}')">
                                <i class="fas fa-cart-plus me-2"></i>
                                أضف إلى السلة
                            </button>
                            <button class="btn btn-success btn-lg w-100 mb-3" onclick="buyNowFromModal('${product.id}')">
                                <i class="fas fa-bolt me-2"></i>
                                اشتري الآن
                            </button>
                        </div>

                        <div class="security-badges">
                            <div class="badge-item">
                                <i class="fas fa-shield-alt text-success"></i>
                                <span>دفع آمن 100%</span>
                            </div>
                            <div class="badge-item">
                                <i class="fas fa-undo text-info"></i>
                                <span>ضمان استرداد المال</span>
                            </div>
                            <div class="badge-item">
                                <i class="fas fa-download text-primary"></i>
                                <span>تحميل فوري</span>
                            </div>
                        </div>
                    </div>

                    <div class="related-products mt-4">
                        <h6>منتجات ذات صلة:</h6>
                        <div class="related-list">
                            ${getRelatedProducts(product, 3).map(related => `
                                <div class="related-item" onclick="viewProductDetails('${related.id}')">
                                    <div class="related-info">
                                        <div class="related-title">${related.title}</div>
                                        <div class="related-price">${formatPrice(related.price)}</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add CSS for product purchase card
    addProductModalCSS();
}

function getRelatedProducts(currentProduct, count = 3) {
    return productsData
        .filter(p => p.categoryKey === currentProduct.categoryKey && p.id !== currentProduct.id)
        .slice(0, count);
}

function changeQuantity(change) {
    const quantityInput = document.getElementById('modalQuantity');
    const totalPriceElement = document.getElementById('modalTotalPrice');

    if (!quantityInput || !totalPriceElement) return;

    let currentQuantity = parseInt(quantityInput.value) || 1;
    let newQuantity = currentQuantity + change;

    if (newQuantity < 1) newQuantity = 1;
    if (newQuantity > 10) newQuantity = 10;

    quantityInput.value = newQuantity;

    // Update total price
    const productId = document.querySelector('[onclick*="addToCartFromModal"]').getAttribute('onclick').match(/'([^']+)'/)[1];
    const product = productsData.find(p => p.id === productId);

    if (product) {
        const totalPrice = product.price * newQuantity;
        totalPriceElement.textContent = formatPrice(totalPrice);
    }
}

function addToCartFromModal(productId) {
    const quantity = parseInt(document.getElementById('modalQuantity').value) || 1;

    for (let i = 0; i < quantity; i++) {
        addToCart(productId);
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    if (modal) modal.hide();
}

function buyNowFromModal(productId) {
    const quantity = parseInt(document.getElementById('modalQuantity').value) || 1;

    // Add to cart
    for (let i = 0; i < quantity; i++) {
        addToCart(productId);
    }

    // Close modal and proceed to checkout
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    if (modal) modal.hide();

    setTimeout(() => {
        proceedToCheckout();
    }, 500);
}

function addProductModalCSS() {
    if (document.getElementById('productModalCSS')) return;

    const style = document.createElement('style');
    style.id = 'productModalCSS';
    style.textContent = `
        .product-purchase-card {
            background: var(--gray-100);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            position: sticky;
            top: 2rem;
        }

        .price-section {
            text-align: center;
            border-bottom: 1px solid var(--gray-300);
            padding-bottom: 1rem;
        }

        .current-price {
            margin-bottom: 0.5rem;
        }

        .price-label {
            display: block;
            font-size: 0.9rem;
            color: var(--gray-600);
            margin-bottom: 0.25rem;
        }

        .price-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .original-price {
            margin-bottom: 0.5rem;
        }

        .discount-badge {
            background: var(--success-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius);
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }

        .security-badges {
            border-top: 1px solid var(--gray-300);
            padding-top: 1rem;
        }

        .badge-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: var(--gray-700);
        }

        .related-products {
            background: white;
            border-radius: var(--border-radius);
            padding: 1rem;
            border: 1px solid var(--gray-200);
        }

        .related-item {
            padding: 0.75rem;
            border-bottom: 1px solid var(--gray-200);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .related-item:hover {
            background: var(--gray-100);
        }

        .related-item:last-child {
            border-bottom: none;
        }

        .related-title {
            font-size: 0.9rem;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
        }

        .related-price {
            font-size: 0.8rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        .data-sample {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            border: 1px solid var(--gray-200);
            margin-bottom: 2rem;
        }

        .table th {
            background: var(--gray-100);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table td {
            font-size: 0.9rem;
        }
    `;

    document.head.appendChild(style);
}

// Initialize categories when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add load more button event listener
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreProducts);
    }
});

// Export functions
window.DatabaseCategories = {
    loadCategories,
    loadProducts,
    filterByCategory,
    viewProductDetails,
    categoriesData,
    productsData
};
