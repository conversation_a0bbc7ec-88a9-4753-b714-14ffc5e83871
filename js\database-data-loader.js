// Database Data Loader - Load categories from JSON files

// Global variables for data
let categoriesData = {};
let productsData = [];
let isDataLoaded = false;

// Load data from JSON files
async function loadDatabaseData() {
    try {
        console.log('Loading database categories from JSON files...');
        
        // Load main categories
        const mainCategoriesResponse = await fetch('data/categories.json');
        const mainCategories = await mainCategoriesResponse.json();
        
        // Load additional categories
        const additionalCategoriesResponse = await fetch('data/additional-categories.json');
        const additionalCategories = await additionalCategoriesResponse.json();
        
        // Merge categories
        categoriesData = { ...mainCategories, ...additionalCategories };
        
        // Generate products data
        generateProductsFromJSON();
        
        isDataLoaded = true;
        console.log('Database data loaded successfully');
        console.log('Categories loaded:', Object.keys(categoriesData).length);
        console.log('Products generated:', productsData.length);
        
        // Trigger data loaded event
        document.dispatchEvent(new CustomEvent('databaseDataLoaded'));
        
        return true;
    } catch (error) {
        console.error('Error loading database data:', error);
        
        // Fallback to hardcoded data if JSON loading fails
        console.log('Falling back to hardcoded data...');
        loadFallbackData();
        return false;
    }
}

// Generate products from JSON data
function generateProductsFromJSON() {
    productsData = [];
    
    Object.entries(categoriesData).forEach(([categoryKey, category]) => {
        category.specializations.forEach(spec => {
            const product = {
                id: spec.id || `${categoryKey}-${spec.name.replace(/\s+/g, '-')}`,
                title: spec.title || `قاعدة بيانات ${spec.name}`,
                category: category.name,
                categoryKey: categoryKey,
                description: spec.description || `قاعدة بيانات شاملة ومحدثة لـ ${spec.name}`,
                count: spec.count,
                price: spec.price,
                originalPrice: Math.round(spec.price * 1.3),
                discount: 23,
                features: [
                    'بيانات محدثة ومؤكدة خلال آخر 30 يوم',
                    'تغطية شاملة للمنطقة الخليجية والعربية',
                    'تصدير بصيغ متعددة: Excel, CSV, JSON, PDF',
                    'دعم فني مجاني على مدار الساعة',
                    'ضمان استرداد المال خلال 7 أيام',
                    'بيانات مؤكدة ومفلترة من المصادر الموثوقة',
                    'تحديثات دورية مجانية لمدة 3 أشهر'
                ],
                rating: (4.2 + Math.random() * 0.8).toFixed(1),
                reviews: Math.floor(Math.random() * 100) + 20,
                specialization: spec.name,
                locations: spec.locations || [],
                specialties: spec.specialties || [],
                types: spec.types || [],
                categories: spec.categories || [],
                services: spec.services || [],
                sectors: spec.sectors || []
            };
            productsData.push(product);
        });
    });
}

// Fallback data in case JSON loading fails
function loadFallbackData() {
    categoriesData = {
        medical: {
            name: 'الطبية والصحية',
            icon: 'fas fa-user-md',
            description: 'قواعد بيانات للمهن الطبية والصحية',
            specializations: [
                {
                    id: 'doctors-consultants',
                    name: 'الأطباء والاستشاريين',
                    count: 2500,
                    price: 299,
                    title: 'قاعدة بيانات شاملة للأطباء والاستشاريين في المملكة العربية السعودية',
                    description: 'تضم أكثر من 2500 طبيب واستشاري في جميع التخصصات الطبية بالمملكة'
                },
                {
                    id: 'hospitals-medical-centers',
                    name: 'المستشفيات والمراكز الطبية',
                    count: 450,
                    price: 199,
                    title: 'قاعدة بيانات المستشفيات والمراكز الطبية الحكومية والخاصة',
                    description: 'دليل شامل لأكثر من 450 مستشفى ومركز طبي في دول الخليج'
                }
            ]
        },
        engineering: {
            name: 'الهندسة والتقنية',
            icon: 'fas fa-cogs',
            description: 'قواعد بيانات للمهندسين والتقنيين',
            specializations: [
                {
                    id: 'engineers-all-specialties',
                    name: 'المهندسين في جميع التخصصات',
                    count: 8000,
                    price: 349,
                    title: 'قاعدة بيانات شاملة للمهندسين في جميع التخصصات الهندسية',
                    description: 'أكبر قاعدة بيانات تضم أكثر من 8000 مهندس في تخصصات مختلفة'
                }
            ]
        }
    };
    
    generateProductsFromJSON();
    isDataLoaded = true;
    document.dispatchEvent(new CustomEvent('databaseDataLoaded'));
}

// Get category data by key
function getCategoryData(categoryKey) {
    return categoriesData[categoryKey] || null;
}

// Get all categories
function getAllCategories() {
    return categoriesData;
}

// Get products by category
function getProductsByCategory(categoryKey) {
    return productsData.filter(product => product.categoryKey === categoryKey);
}

// Get product by ID
function getProductById(productId) {
    return productsData.find(product => product.id === productId);
}

// Search products
function searchProducts(query) {
    if (!query) return productsData;
    
    const searchTerm = query.toLowerCase();
    return productsData.filter(product => 
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm) ||
        product.specialization.toLowerCase().includes(searchTerm)
    );
}

// Filter products
function filterProducts(filters = {}) {
    let filtered = [...productsData];
    
    // Category filter
    if (filters.category) {
        filtered = filtered.filter(product => product.categoryKey === filters.category);
    }
    
    // Price range filter
    if (filters.priceRange) {
        const [min, max] = filters.priceRange.split('-').map(p => p === '+' ? Infinity : parseInt(p));
        filtered = filtered.filter(product => 
            product.price >= min && (max === Infinity || product.price <= max)
        );
    }
    
    // Search filter
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filtered = filtered.filter(product => 
            product.title.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm)
        );
    }
    
    // Sort filter
    if (filters.sort) {
        switch (filters.sort) {
            case 'price-low':
                filtered.sort((a, b) => a.price - b.price);
                break;
            case 'price-high':
                filtered.sort((a, b) => b.price - a.price);
                break;
            case 'count-high':
                filtered.sort((a, b) => b.count - a.count);
                break;
            case 'name':
                filtered.sort((a, b) => a.title.localeCompare(b.title, 'ar'));
                break;
            default:
                // relevance - keep original order
                break;
        }
    }
    
    return filtered;
}

// Get statistics
function getDatabaseStatistics() {
    const totalProducts = productsData.length;
    const totalContacts = productsData.reduce((sum, product) => sum + product.count, 0);
    const totalCategories = Object.keys(categoriesData).length;
    const averagePrice = Math.round(productsData.reduce((sum, product) => sum + product.price, 0) / totalProducts);
    
    return {
        totalProducts,
        totalContacts,
        totalCategories,
        averagePrice,
        priceRange: {
            min: Math.min(...productsData.map(p => p.price)),
            max: Math.max(...productsData.map(p => p.price))
        }
    };
}

// Initialize data loading when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    loadDatabaseData();
});

// Export functions for global use
window.DatabaseDataLoader = {
    loadDatabaseData,
    getCategoryData,
    getAllCategories,
    getProductsByCategory,
    getProductById,
    searchProducts,
    filterProducts,
    getDatabaseStatistics,
    isDataLoaded: () => isDataLoaded,
    categoriesData: () => categoriesData,
    productsData: () => productsData
};
