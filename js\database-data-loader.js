// Database Data Loader - Load categories from JSON files

// Global variables for data
let categoriesData = {};
let productsData = [];
let isDataLoaded = false;

// Load data from embedded JSON data
async function loadDatabaseData() {
    try {
        console.log('Loading database categories from embedded data...');

        // Use embedded data instead of fetching JSON files
        categoriesData = getEmbeddedCategoriesData();

        // Generate products data
        generateProductsFromJSON();

        isDataLoaded = true;
        console.log('Database data loaded successfully');
        console.log('Categories loaded:', Object.keys(categoriesData).length);
        console.log('Products generated:', productsData.length);

        // Trigger data loaded event
        document.dispatchEvent(new CustomEvent('databaseDataLoaded'));

        return true;
    } catch (error) {
        console.error('Error loading database data:', error);

        // Fallback to hardcoded data if loading fails
        console.log('Falling back to hardcoded data...');
        loadFallbackData();
        return false;
    }
}

// Generate products from JSON data
function generateProductsFromJSON() {
    productsData = [];
    
    Object.entries(categoriesData).forEach(([categoryKey, category]) => {
        category.specializations.forEach(spec => {
            const product = {
                id: spec.id || `${categoryKey}-${spec.name.replace(/\s+/g, '-')}`,
                title: spec.title || `قاعدة بيانات ${spec.name}`,
                category: category.name,
                categoryKey: categoryKey,
                description: spec.description || `قاعدة بيانات شاملة ومحدثة لـ ${spec.name}`,
                count: spec.count,
                price: spec.price,
                originalPrice: Math.round(spec.price * 1.3),
                discount: 23,
                features: [
                    'بيانات محدثة ومؤكدة خلال آخر 30 يوم',
                    'تغطية شاملة للمنطقة الخليجية والعربية',
                    'تصدير بصيغ متعددة: Excel, CSV, JSON, PDF',
                    'دعم فني مجاني على مدار الساعة',
                    'ضمان استرداد المال خلال 7 أيام',
                    'بيانات مؤكدة ومفلترة من المصادر الموثوقة',
                    'تحديثات دورية مجانية لمدة 3 أشهر'
                ],
                rating: (4.2 + Math.random() * 0.8).toFixed(1),
                reviews: Math.floor(Math.random() * 100) + 20,
                specialization: spec.name,
                locations: spec.locations || [],
                specialties: spec.specialties || [],
                types: spec.types || [],
                categories: spec.categories || [],
                services: spec.services || [],
                sectors: spec.sectors || []
            };
            productsData.push(product);
        });
    });
}

// Fallback data in case JSON loading fails
function loadFallbackData() {
    categoriesData = {
        medical: {
            name: 'الطبية والصحية',
            icon: 'fas fa-user-md',
            description: 'قواعد بيانات للمهن الطبية والصحية',
            specializations: [
                {
                    id: 'doctors-consultants',
                    name: 'الأطباء والاستشاريين',
                    count: 2500,
                    price: 299,
                    title: 'قاعدة بيانات شاملة للأطباء والاستشاريين في المملكة العربية السعودية',
                    description: 'تضم أكثر من 2500 طبيب واستشاري في جميع التخصصات الطبية بالمملكة'
                },
                {
                    id: 'hospitals-medical-centers',
                    name: 'المستشفيات والمراكز الطبية',
                    count: 450,
                    price: 199,
                    title: 'قاعدة بيانات المستشفيات والمراكز الطبية الحكومية والخاصة',
                    description: 'دليل شامل لأكثر من 450 مستشفى ومركز طبي في دول الخليج'
                }
            ]
        },
        engineering: {
            name: 'الهندسة والتقنية',
            icon: 'fas fa-cogs',
            description: 'قواعد بيانات للمهندسين والتقنيين',
            specializations: [
                {
                    id: 'engineers-all-specialties',
                    name: 'المهندسين في جميع التخصصات',
                    count: 8000,
                    price: 349,
                    title: 'قاعدة بيانات شاملة للمهندسين في جميع التخصصات الهندسية',
                    description: 'أكبر قاعدة بيانات تضم أكثر من 8000 مهندس في تخصصات مختلفة'
                }
            ]
        }
    };
    
    generateProductsFromJSON();
    isDataLoaded = true;
    document.dispatchEvent(new CustomEvent('databaseDataLoaded'));
}

// Get category data by key
function getCategoryData(categoryKey) {
    return categoriesData[categoryKey] || null;
}

// Get all categories
function getAllCategories() {
    return categoriesData;
}

// Get products by category
function getProductsByCategory(categoryKey) {
    return productsData.filter(product => product.categoryKey === categoryKey);
}

// Get product by ID
function getProductById(productId) {
    return productsData.find(product => product.id === productId);
}

// Search products
function searchProducts(query) {
    if (!query) return productsData;
    
    const searchTerm = query.toLowerCase();
    return productsData.filter(product => 
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm) ||
        product.specialization.toLowerCase().includes(searchTerm)
    );
}

// Filter products
function filterProducts(filters = {}) {
    let filtered = [...productsData];
    
    // Category filter
    if (filters.category) {
        filtered = filtered.filter(product => product.categoryKey === filters.category);
    }
    
    // Price range filter
    if (filters.priceRange) {
        const [min, max] = filters.priceRange.split('-').map(p => p === '+' ? Infinity : parseInt(p));
        filtered = filtered.filter(product => 
            product.price >= min && (max === Infinity || product.price <= max)
        );
    }
    
    // Search filter
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filtered = filtered.filter(product => 
            product.title.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm)
        );
    }
    
    // Sort filter
    if (filters.sort) {
        switch (filters.sort) {
            case 'price-low':
                filtered.sort((a, b) => a.price - b.price);
                break;
            case 'price-high':
                filtered.sort((a, b) => b.price - a.price);
                break;
            case 'count-high':
                filtered.sort((a, b) => b.count - a.count);
                break;
            case 'name':
                filtered.sort((a, b) => a.title.localeCompare(b.title, 'ar'));
                break;
            default:
                // relevance - keep original order
                break;
        }
    }
    
    return filtered;
}

// Get statistics
function getDatabaseStatistics() {
    const totalProducts = productsData.length;
    const totalContacts = productsData.reduce((sum, product) => sum + product.count, 0);
    const totalCategories = Object.keys(categoriesData).length;
    const averagePrice = Math.round(productsData.reduce((sum, product) => sum + product.price, 0) / totalProducts);
    
    return {
        totalProducts,
        totalContacts,
        totalCategories,
        averagePrice,
        priceRange: {
            min: Math.min(...productsData.map(p => p.price)),
            max: Math.max(...productsData.map(p => p.price))
        }
    };
}

// Get embedded categories data
function getEmbeddedCategoriesData() {
    return {
        medical: {
            name: 'الطبية والصحية',
            icon: 'fas fa-user-md',
            description: 'قواعد بيانات للمهن الطبية والصحية',
            specializations: [
                {
                    id: 'doctors-consultants',
                    name: 'الأطباء والاستشاريين',
                    count: 2500,
                    price: 299,
                    title: 'قاعدة بيانات شاملة للأطباء والاستشاريين في المملكة العربية السعودية',
                    description: 'تضم أكثر من 2500 طبيب واستشاري في جميع التخصصات الطبية بالمملكة مع بيانات الاتصال المحدثة والتخصصات الدقيقة',
                    locations: ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة'],
                    specialties: ['باطنية', 'جراحة', 'أطفال', 'نساء وولادة', 'عيون', 'أنف وأذن', 'جلدية', 'نفسية']
                },
                {
                    id: 'hospitals-medical-centers',
                    name: 'المستشفيات والمراكز الطبية',
                    count: 450,
                    price: 199,
                    title: 'قاعدة بيانات المستشفيات والمراكز الطبية الحكومية والخاصة',
                    description: 'دليل شامل لأكثر من 450 مستشفى ومركز طبي في دول الخليج مع معلومات الإدارة والتواصل المباشر',
                    locations: ['السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان'],
                    types: ['مستشفيات حكومية', 'مستشفيات خاصة', 'مراكز طبية', 'عيادات متخصصة']
                },
                {
                    id: 'specialized-clinics',
                    name: 'العيادات الطبية المتخصصة',
                    count: 1200,
                    price: 149,
                    title: 'قاعدة بيانات العيادات الطبية المتخصصة في المنطقة الخليجية',
                    description: 'تشمل 1200 عيادة طبية متخصصة مع بيانات الأطباء المسؤولين وأرقام التواصل المباشر',
                    locations: ['الرياض', 'جدة', 'الدمام', 'أبوظبي', 'دبي', 'الكويت'],
                    specialties: ['أسنان', 'عيون', 'جلدية', 'تجميل', 'علاج طبيعي', 'نفسية']
                },
                {
                    id: 'nurses-medical-staff',
                    name: 'الممرضين والكوادر الطبية',
                    count: 3200,
                    price: 179,
                    title: 'قاعدة بيانات شاملة للممرضين والكوادر الطبية المرخصين',
                    description: 'أكبر قاعدة بيانات للكوادر التمريضية والطبية تضم أكثر من 3200 ممرض وفني طبي مرخص',
                    locations: ['جميع مناطق المملكة', 'دول الخليج'],
                    categories: ['ممرضين', 'فنيين طبيين', 'أخصائيين', 'مساعدين طبيين']
                },
                {
                    id: 'physiotherapy-rehabilitation',
                    name: 'العلاج الطبيعي والتأهيل الطبي',
                    count: 1500,
                    price: 159,
                    title: 'قاعدة بيانات أخصائيي العلاج الطبيعي ومراكز التأهيل الطبي',
                    description: 'دليل شامل لـ 1500 أخصائي علاج طبيعي ومركز تأهيل طبي معتمد في المنطقة',
                    locations: ['الرياض', 'جدة', 'الدمام', 'مكة'],
                    services: ['علاج طبيعي', 'تأهيل حركي', 'علاج وظيفي', 'علاج النطق']
                }
            ]
        },
        engineering: {
            name: 'الهندسة والتقنية',
            icon: 'fas fa-cogs',
            description: 'قواعد بيانات للمهندسين والتقنيين',
            specializations: [
                {
                    id: 'engineers-all-specialties',
                    name: 'المهندسين في جميع التخصصات',
                    count: 8000,
                    price: 349,
                    title: 'قاعدة بيانات شاملة للمهندسين في جميع التخصصات الهندسية',
                    description: 'أكبر قاعدة بيانات تضم أكثر من 8000 مهندس في تخصصات مختلفة: مدني، كهربائي، ميكانيكي، معماري، صناعي وغيرها',
                    locations: ['السعودية', 'الإمارات', 'الكويت', 'قطر'],
                    specialties: ['مدني', 'كهربائي', 'ميكانيكي', 'معماري', 'صناعي', 'كيميائي', 'بترول', 'حاسوب']
                },
                {
                    id: 'consulting-engineers',
                    name: 'المهندسين الاستشاريين والخبراء',
                    count: 1200,
                    price: 399,
                    title: 'قاعدة بيانات المهندسين الاستشاريين والخبراء التقنيين',
                    description: 'دليل حصري لـ 1200 مهندس استشاري وخبير تقني في الشركات الكبرى ومكاتب الاستشارات الهندسية',
                    locations: ['الرياض', 'جدة', 'الدمام', 'أبوظبي', 'دبي'],
                    experience: ['أكثر من 10 سنوات خبرة', 'مرخصين', 'معتمدين دولياً']
                },
                {
                    id: 'oil-gas-industry',
                    name: 'صناعة النفط والغاز الطبيعي',
                    count: 1400,
                    price: 499,
                    title: 'قاعدة بيانات حصرية للعاملين في صناعة النفط والغاز الطبيعي',
                    description: 'أهم قاعدة بيانات تضم 1400 متخصص في شركات النفط والغاز والاستكشاف والإنتاج والتكرير',
                    locations: ['الظهران', 'رأس تنورة', 'الجبيل', 'ينبع'],
                    specialties: ['استكشاف', 'إنتاج', 'تكرير', 'توزيع', 'تسويق']
                }
            ]
        },
        business: {
            name: 'الأعمال والإدارة',
            icon: 'fas fa-briefcase',
            description: 'قواعد بيانات لرجال الأعمال والإدارة',
            specializations: [
                {
                    id: 'executive-managers',
                    name: 'المدراء التنفيذيين والإداريين',
                    count: 5200,
                    price: 399,
                    title: 'قاعدة بيانات شاملة للمدراء التنفيذيين والإداريين في القطاع الخاص',
                    description: 'تضم أكثر من 5200 مدير تنفيذي وإداري في الشركات والمؤسسات الكبرى بدول الخليج',
                    locations: ['الرياض', 'جدة', 'الدمام', 'أبوظبي', 'دبي', 'الكويت', 'الدوحة'],
                    positions: ['مدير عام', 'مدير تنفيذي', 'مدير إدارة', 'مدير فرع', 'مدير عمليات']
                },
                {
                    id: 'ceo-founders',
                    name: 'الرؤساء التنفيذيين ومؤسسي الشركات',
                    count: 450,
                    price: 599,
                    title: 'قاعدة بيانات حصرية للرؤساء التنفيذيين ومؤسسي الشركات',
                    description: 'أهم قاعدة بيانات تضم 450 رئيس تنفيذي ومؤسس شركة في المنطقة الخليجية',
                    locations: ['دول الخليج'],
                    company_sizes: ['شركات كبرى', 'متعددة الجنسيات', 'شركات مدرجة', 'شركات عائلية كبيرة']
                },
                {
                    id: 'entrepreneurs',
                    name: 'رواد الأعمال والمبتكرين',
                    count: 1200,
                    price: 449,
                    title: 'قاعدة بيانات رواد الأعمال والمبتكرين في الشركات الناشئة والتقنية',
                    description: 'دليل شامل لـ 1200 رائد أعمال ومبتكر في الشركات الناشئة والتقنية والمشاريع الريادية',
                    locations: ['الرياض', 'جدة', 'الدمام', 'أبوظبي', 'دبي'],
                    sectors: ['تقنية', 'تجارة إلكترونية', 'فنتك', 'صحة رقمية', 'تعليم إلكتروني']
                }
            ]
        },
        finance: {
            name: 'المالية والمصرفية',
            icon: 'fas fa-university',
            description: 'قواعد بيانات للقطاع المالي والمصرفي',
            specializations: [
                {
                    id: 'banks-financial-institutions',
                    name: 'البنوك والمؤسسات المالية',
                    count: 2050,
                    price: 399,
                    title: 'قاعدة بيانات شاملة للبنوك والمؤسسات المالية في دول الخليج',
                    description: 'دليل حصري لأكثر من 2050 بنك ومؤسسة مالية مع بيانات الإدارة العليا والمدراء التنفيذيين',
                    locations: ['السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان'],
                    types: ['بنوك تجارية', 'بنوك إسلامية', 'بنوك استثمارية', 'مؤسسات تمويل', 'شركات تأمين']
                },
                {
                    id: 'accountants-auditors',
                    name: 'المحاسبين والمراجعين المعتمدين',
                    count: 4500,
                    price: 299,
                    title: 'قاعدة بيانات شاملة للمحاسبين والمراجعين المعتمدين',
                    description: 'أكبر قاعدة بيانات تضم 4500 محاسب ومراجع معتمد في الشركات والمكاتب المحاسبية',
                    locations: ['جميع دول الخليج'],
                    certifications: ['CPA', 'CMA', 'CIA', 'SOCPA', 'ACCA', 'محاسب قانوني']
                }
            ]
        },
        retail: {
            name: 'التجارة والبيع بالتجزئة',
            icon: 'fas fa-shopping-cart',
            description: 'قواعد بيانات للقطاع التجاري',
            specializations: [
                {
                    id: 'retail-stores',
                    name: 'المتاجر والمحلات التجارية',
                    count: 8300,
                    price: 249,
                    title: 'قاعدة بيانات شاملة للمتاجر والمحلات التجارية في المولات والأسواق',
                    description: 'أكبر قاعدة بيانات تضم 8300 متجر ومحل تجاري مع بيانات الملاك والمدراء',
                    locations: ['جميع المدن السعودية', 'دول الخليج'],
                    categories: ['ملابس', 'إلكترونيات', 'مواد غذائية', 'مستحضرات تجميل', 'أثاث']
                },
                {
                    id: 'customers-clients',
                    name: 'قواعد بيانات العملاء والزبائن',
                    count: 14700,
                    price: 299,
                    title: 'قاعدة بيانات شاملة للعملاء والزبائن في القطاعات التجارية المختلفة',
                    description: 'أكبر قاعدة بيانات عملاء تضم 14700 عميل وزبون مصنف حسب الاهتمامات والقطاعات',
                    locations: ['دول الخليج العربي'],
                    segments: ['عملاء أفراد', 'عملاء شركات', 'عملاء VIP', 'عملاء محتملين']
                }
            ]
        },
        education: {
            name: 'التعليم والتدريب',
            icon: 'fas fa-graduation-cap',
            description: 'قواعد بيانات للمؤسسات التعليمية',
            specializations: [
                {
                    id: 'teachers-educators',
                    name: 'المعلمين والمعلمات',
                    count: 8500,
                    price: 249,
                    title: 'قاعدة بيانات شاملة للمعلمين والمعلمات في جميع المراحل التعليمية',
                    description: 'أكبر قاعدة بيانات تضم 8500 معلم ومعلمة في المدارس الحكومية والأهلية والدولية',
                    locations: ['جميع مناطق المملكة'],
                    levels: ['ابتدائي', 'متوسط', 'ثانوي', 'جامعي'],
                    subjects: ['رياضيات', 'علوم', 'لغة عربية', 'إنجليزي', 'اجتماعيات', 'تربية إسلامية']
                },
                {
                    id: 'schools-institutions',
                    name: 'المدارس والمؤسسات التعليمية',
                    count: 4000,
                    price: 199,
                    title: 'قاعدة بيانات المدارس والمؤسسات التعليمية الحكومية والخاصة والدولية',
                    description: 'دليل شامل لأكثر من 4000 مدرسة ومؤسسة تعليمية مع بيانات الإدارة والتواصل',
                    locations: ['السعودية', 'الإمارات', 'الكويت', 'قطر'],
                    types: ['حكومية', 'أهلية', 'دولية', 'معاهد', 'مراكز تدريب']
                }
            ]
        }
    };
}

// Initialize data loading when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    loadDatabaseData();
});

// Export functions for global use
window.DatabaseDataLoader = {
    loadDatabaseData,
    getCategoryData,
    getAllCategories,
    getProductsByCategory,
    getProductById,
    searchProducts,
    filterProducts,
    getDatabaseStatistics,
    isDataLoaded: () => isDataLoaded,
    categoriesData: () => categoriesData,
    productsData: () => productsData
};
